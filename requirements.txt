# 医学大语言模型微调依赖包
# 核心框架
torch>=2.0.0
transformers>=4.35.0
tokenizers>=0.14.0
datasets>=2.14.0
accelerate>=0.24.0

# 高效微调
peft>=0.6.0
bitsandbytes>=0.41.0
scipy>=1.10.0
unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git
unsloth_zoo
trl>=0.7.0

# 训练和评估
wandb>=0.16.0
scikit-learn>=1.3.0
numpy>=1.24.0
pandas>=2.0.0

# RAG系统
sentence-transformers>=2.2.0
raganything[all]
lightrag
chromadb>=0.4.0

# 工具库
tqdm>=4.65.0
matplotlib>=3.7.0
seaborn>=0.12.0
jupyter>=1.0.0
python-dotenv>=1.0.0
huggingface_hub>=0.20.0
PyYAML>=6.0

# 可选：如果需要使用Qwen模型的特殊功能
# modelscope>=1.9.0
# transformers_stream_generator>=0.0.4

# 开发工具
black>=23.0.0
flake8>=6.0.0
pytest>=7.0.0

